// Debug script to analyze eBay search results page structure
console.log('=== SEARCH RESULT CONTAINERS ===');

// Check for old layout selectors
const oldItems = document.querySelectorAll('li.s-item');
console.log('li.s-item elements:', oldItems.length);

// Check for new layout selectors  
const newItems = document.querySelectorAll('li.su-card-container');
console.log('li.su-card-container elements:', newItems.length);

// Look for any li elements that might be search results
const allLiElements = document.querySelectorAll('li');
console.log('Total li elements:', allLiElements.length);

// Find search results by looking for price patterns
const searchResults = [];
for (let li of allLiElements) {
  if (li.textContent.includes('$') && li.textContent.match(/\$[\d,]+\.?\d*/)) {
    searchResults.push(li);
  }
}
console.log('Found', searchResults.length, 'potential search result items');

// Analyze the first few search results
console.log('=== ANALYZING FIRST 3 SEARCH RESULTS ===');
for (let i = 0; i < Math.min(3, searchResults.length); i++) {
  const item = searchResults[i];
  console.log(`Search result ${i + 1}:`, {
    className: item.className,
    id: item.id,
    dataset: Object.keys(item.dataset),
    hasSellerInfo: item.textContent.includes('(') && item.textContent.includes('%'),
    outerHTML: item.outerHTML.substring(0, 200) + '...'
  });
  
  // Look for seller information within this item
  const sellerElements = item.querySelectorAll('*');
  for (let elem of sellerElements) {
    if (elem.textContent && elem.textContent.match(/\w+\s*\(\d+\)\s*\d+%/)) {
      console.log(`  Found seller info in ${elem.tagName}.${elem.className}:`, elem.textContent.trim());
      console.log(`  Parent: ${elem.parentElement?.tagName}.${elem.parentElement?.className}`);
      break;
    }
  }
}

// Look for specific seller patterns
console.log('=== SELLER INFO PATTERNS ===');
const sellerPatterns = document.querySelectorAll('*');
let sellerCount = 0;
for (let elem of sellerPatterns) {
  if (elem.textContent && elem.textContent.match(/\w+\s*\(\d+\)\s*\d+%/) && sellerCount < 5) {
    console.log('Found seller info:', {
      text: elem.textContent.trim(),
      tagName: elem.tagName,
      className: elem.className,
      parentClassName: elem.parentElement?.className,
      grandParentClassName: elem.parentElement?.parentElement?.className
    });
    sellerCount++;
  }
}
